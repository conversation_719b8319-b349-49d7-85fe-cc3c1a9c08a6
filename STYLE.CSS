/* Pakistani Music Player - Premium Styling */
:root {
    /* Light Theme Colors */
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;

    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --bg-card: #ffffff;

    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;

    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-heavy: 0 20px 25px rgba(0, 0, 0, 0.1);

    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-secondary: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
    --gradient-accent: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-card: #1e293b;

    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;

    --border-color: #334155;
    --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.3);
    --shadow-heavy: 0 20px 25px rgba(0, 0, 0, 0.4);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    transition: all 0.3s ease;
}

/* Music Player Container */
.music-player-container {
    min-height: 100vh;
    background: var(--bg-primary);
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header Styles */
.player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 100;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo i {
    font-size: 2rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-controls {
    display: flex;
    gap: 1rem;
}

.theme-toggle,
.playlist-toggle {
    width: 3rem;
    height: 3rem;
    border: none;
    border-radius: 50%;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-toggle:hover,
.playlist-toggle:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Main Player Styles */
.main-player {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    max-width: 500px;
    margin: 0 auto;
    width: 100%;
}

/* Album Art Container */
.album-art-container {
    position: relative;
    margin-bottom: 2rem;
}

.album-art {
    width: 280px;
    height: 280px;
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-heavy);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.album-art img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.vinyl-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: var(--bg-card);
    border-radius: 50%;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.vinyl-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--text-primary);
    border-radius: 50%;
}

.album-art img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.vinyl-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: var(--bg-card);
    border-radius: 50%;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.vinyl-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    background: var(--text-primary);
    border-radius: 50%;
}

/* Audio Visualizer */
.audio-visualizer {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 4px;
    align-items: end;
}

.audio-visualizer .bar {
    width: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
    animation: visualizer 1.5s ease-in-out infinite;
}

.audio-visualizer .bar:nth-child(1) { height: 20px; animation-delay: 0s; }
.audio-visualizer .bar:nth-child(2) { height: 30px; animation-delay: 0.1s; }
.audio-visualizer .bar:nth-child(3) { height: 25px; animation-delay: 0.2s; }
.audio-visualizer .bar:nth-child(4) { height: 35px; animation-delay: 0.3s; }
.audio-visualizer .bar:nth-child(5) { height: 20px; animation-delay: 0.4s; }

@keyframes visualizer {
    0%, 100% { transform: scaleY(1); }
    50% { transform: scaleY(0.3); }
}

/* Song Information */
.song-info {
    text-align: center;
    margin-bottom: 2rem;
    width: 100%;
}

.song-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.artist-name {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.song-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-muted);
}

/* Progress Bar */
.progress-container {
    width: 100%;
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    position: relative;
    cursor: pointer;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 3px;
    width: 0%;
    transition: width 0.1s ease;
    position: relative;
}

.progress-thumb {
    position: absolute;
    top: 50%;
    right: -8px;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-bar:hover .progress-thumb {
    opacity: 1;
}

/* Main Controls */
.main-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.control-btn {
    width: 3.5rem;
    height: 3.5rem;
    border: none;
    border-radius: 50%;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.play-btn {
    width: 4.5rem;
    height: 4.5rem;
    background: var(--gradient-primary);
    color: white;
    font-size: 1.5rem;
}

.play-btn:hover {
    background: var(--gradient-secondary);
    transform: translateY(-3px) scale(1.05);
}

.shuffle-btn.active,
.repeat-btn.active {
    background: var(--gradient-accent);
    color: white;
}