// Pakistani Music Player - Advanced JavaScript Implementation
class PakistaniMusicPlayer {
    constructor() {
        this.audio = document.getElementById('audioPlayer');
        this.currentSongIndex = 0;
        this.isPlaying = false;
        this.isShuffled = false;
        this.repeatMode = 'none'; // none, one, all
        this.currentPlaylist = [];
        this.allSongs = [];
        this.currentTab = 'dramas';
        
        // Initialize player
        this.initializePlayer();
        this.loadSongs();
        this.setupEventListeners();
        this.loadUserPreferences();
    }

    initializePlayer() {
        // Get DOM elements
        this.playBtn = document.getElementById('playBtn');
        this.prevBtn = document.getElementById('prevBtn');
        this.nextBtn = document.getElementById('nextBtn');
        this.shuffleBtn = document.getElementById('shuffleBtn');
        this.repeatBtn = document.getElementById('repeatBtn');
        this.volumeBtn = document.getElementById('volumeBtn');
        this.volumeSlider = document.getElementById('volumeSlider');
        this.progressBar = document.getElementById('progressBar');
        this.progressFill = document.getElementById('progressFill');
        this.progressThumb = document.getElementById('progressThumb');
        this.currentTime = document.getElementById('currentTime');
        this.totalTime = document.getElementById('totalTime');
        this.songTitle = document.getElementById('songTitle');
        this.artistName = document.getElementById('artistName');
        this.albumImage = document.getElementById('albumImage');
        this.themeToggle = document.getElementById('themeToggle');
        this.playlistToggle = document.getElementById('playlistToggle');
        this.playlistSection = document.getElementById('playlistSection');
        
        // Set initial volume
        this.audio.volume = 0.7;
        this.volumeSlider.value = 70;
    }

    loadSongs() {
        // Pakistani Drama OSTs
        const dramaOSTs = [
            {
                title: "Qurban OST",
                artist: "Drama OST",
                src: "audio/qurban-ost.mp3",
                duration: "3:45",
                category: "dramas",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZmY2NjY2Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iI2ZmNjY2NiIvPgo8L3N2Zz4K"
            },
            {
                title: "Meem Sy Muhabat",
                artist: "Drama OST",
                src: "audio/meem-sy-muhabat.mp3",
                duration: "4:12",
                category: "dramas",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY5OWZmIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iIzY2OTlmZiIvPgo8L3N2Zz4K"
            },
            {
                title: "Sher OST",
                artist: "Drama OST",
                src: "audio/sher-ost.mp3",
                duration: "3:28",
                category: "dramas",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZmY5OTY2Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iI2ZmOTk2NiIvPgo8L3N2Zz4K"
            }
        ];

        // Popular Pakistani Songs
        const popularSongs = [
            {
                title: "Jhol",
                artist: "Pakistani Song",
                src: "audio/jhol.mp3",
                duration: "4:05",
                category: "songs",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjZmZjk5Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iIzY2ZmY5OSIvPgo8L3N2Zz4K"
            },
            {
                title: "Ya Qurban",
                artist: "Pakistani Song",
                src: "audio/ya-qurban.mp3",
                duration: "3:52",
                category: "songs",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjOTk2NmZmIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iIzk5NjZmZiIvPgo8L3N2Zz4K"
            },
            {
                title: "Pal Pal Jeena Muhal",
                artist: "Pakistani Song",
                src: "audio/pal-pal-jeena.mp3",
                duration: "4:18",
                category: "songs",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZmZjYzY2Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iI2ZmY2M2NiIvPgo8L3N2Zz4K"
            }
        ];

        // Pashto Songs
        const pashtoSongs = [
            {
                title: "Adam Khana Charsi",
                artist: "Pashto Song",
                src: "audio/adam-khana-charsi.mp3",
                duration: "3:35",
                category: "pashto",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjZmZmNjIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iIzY2ZmZjYyIvPgo8L3N2Zz4K"
            },
            {
                title: "Allah Wash Wash Wash",
                artist: "Pashto Song",
                src: "audio/allah-wash-wash.mp3",
                duration: "4:22",
                category: "pashto",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjY2M2NmZmIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iI2NjNjZmZiIvPgo8L3N2Zz4K"
            },
            {
                title: "Allah Jar Jar Jarr",
                artist: "Pashto Song",
                src: "audio/allah-jar-jar.mp3",
                duration: "3:48",
                category: "pashto",
                albumArt: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZmY2NmNjIi8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNmZmZmZmYiLz4KPGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iI2ZmNjZjYyIvPgo8L3N2Zz4K"
            }
        ];

        // Combine all songs
        this.allSongs = [...dramaOSTs, ...popularSongs, ...pashtoSongs];
        this.currentPlaylist = this.allSongs.filter(song => song.category === this.currentTab);
    }

    setupEventListeners() {
        // Play/Pause button
        this.playBtn.addEventListener('click', () => this.togglePlay());
        
        // Previous/Next buttons
        this.prevBtn.addEventListener('click', () => this.previousSong());
        this.nextBtn.addEventListener('click', () => this.nextSong());
        
        // Shuffle button
        this.shuffleBtn.addEventListener('click', () => this.toggleShuffle());
        
        // Repeat button
        this.repeatBtn.addEventListener('click', () => this.toggleRepeat());
        
        // Volume controls
        this.volumeBtn.addEventListener('click', () => this.toggleMute());
        this.volumeSlider.addEventListener('input', (e) => this.setVolume(e.target.value));
        
        // Progress bar
        this.progressBar.addEventListener('click', (e) => this.seekTo(e));
        
        // Theme toggle
        this.themeToggle.addEventListener('click', () => this.toggleTheme());
        
        // Playlist toggle
        this.playlistToggle.addEventListener('click', () => this.togglePlaylist());
        
        // Audio events
        this.audio.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('ended', () => this.handleSongEnd());
        this.audio.addEventListener('error', (e) => this.handleAudioError(e));
        
        // Playlist tabs
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });
        
        // Song items
        document.querySelectorAll('.song-item').forEach((item, index) => {
            item.addEventListener('click', () => this.selectSong(index));
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    togglePlay() {
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }

    play() {
        if (this.currentPlaylist.length === 0) return;
        
        if (!this.audio.src || this.audio.src === '') {
            this.loadCurrentSong();
        }
        
        this.audio.play().then(() => {
            this.isPlaying = true;
            this.updatePlayButton();
            this.startVisualizer();
        }).catch(error => {
            console.error('Error playing audio:', error);
            this.showNotification('Error playing audio. Please check the file.');
        });
    }

    pause() {
        this.audio.pause();
        this.isPlaying = false;
        this.updatePlayButton();
        this.stopVisualizer();
    }

    previousSong() {
        if (this.currentPlaylist.length === 0) return;
        
        if (this.isShuffled) {
            this.currentSongIndex = Math.floor(Math.random() * this.currentPlaylist.length);
        } else {
            this.currentSongIndex = this.currentSongIndex > 0 ? 
                this.currentSongIndex - 1 : this.currentPlaylist.length - 1;
        }
        
        this.loadCurrentSong();
        if (this.isPlaying) {
            this.play();
        }
    }

    nextSong() {
        if (this.currentPlaylist.length === 0) return;
        
        if (this.isShuffled) {
            this.currentSongIndex = Math.floor(Math.random() * this.currentPlaylist.length);
        } else {
            this.currentSongIndex = this.currentSongIndex < this.currentPlaylist.length - 1 ? 
                this.currentSongIndex + 1 : 0;
        }
        
        this.loadCurrentSong();
        if (this.isPlaying) {
            this.play();
        }
    }

    toggleShuffle() {
        this.isShuffled = !this.isShuffled;
        this.shuffleBtn.classList.toggle('active', this.isShuffled);
        this.saveUserPreferences();
        this.showNotification(this.isShuffled ? 'Shuffle enabled' : 'Shuffle disabled');
    }

    toggleRepeat() {
        const modes = ['none', 'one', 'all'];
        const currentIndex = modes.indexOf(this.repeatMode);
        this.repeatMode = modes[(currentIndex + 1) % modes.length];
        
        this.repeatBtn.classList.toggle('active', this.repeatMode !== 'none');
        
        const icon = this.repeatBtn.querySelector('i');
        if (this.repeatMode === 'one') {
            icon.className = 'fas fa-redo-alt';
        } else {
            icon.className = 'fas fa-redo';
        }
        
        this.saveUserPreferences();
        this.showNotification(`Repeat: ${this.repeatMode}`);
    }

    toggleMute() {
        if (this.audio.volume > 0) {
            this.previousVolume = this.audio.volume;
            this.setVolume(0);
        } else {
            this.setVolume(this.previousVolume || 0.7);
        }
    }

    setVolume(value) {
        const volume = value / 100;
        this.audio.volume = volume;
        this.volumeSlider.value = value;
        
        const icon = this.volumeBtn.querySelector('i');
        if (volume === 0) {
            icon.className = 'fas fa-volume-mute';
        } else if (volume < 0.5) {
            icon.className = 'fas fa-volume-down';
        } else {
            icon.className = 'fas fa-volume-up';
        }
        
        this.saveUserPreferences();
    }

    seekTo(event) {
        const rect = this.progressBar.getBoundingClientRect();
        const percent = (event.clientX - rect.left) / rect.width;
        const seekTime = percent * this.audio.duration;
        this.audio.currentTime = seekTime;
    }

    updateProgress() {
        if (this.audio.duration) {
            const percent = (this.audio.currentTime / this.audio.duration) * 100;
            this.progressFill.style.width = `${percent}%`;
            this.progressThumb.style.left = `${percent}%`;

            this.currentTime.textContent = this.formatTime(this.audio.currentTime);
        }
    }

    updateDuration() {
        this.totalTime.textContent = this.formatTime(this.audio.duration);
    }

    formatTime(seconds) {
        const mins = Math.floor(seconds / 60);
        const secs = Math.floor(seconds % 60);
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    }

    loadCurrentSong() {
        if (this.currentPlaylist.length === 0) return;

        const song = this.currentPlaylist[this.currentSongIndex];
        this.audio.src = song.src;
        this.songTitle.textContent = song.title;
        this.artistName.textContent = song.artist;
        this.albumImage.src = song.albumArt;

        this.updatePlaylistUI();
    }

    updatePlayButton() {
        const icon = this.playBtn.querySelector('i');
        icon.className = this.isPlaying ? 'fas fa-pause' : 'fas fa-play';
    }

    handleSongEnd() {
        if (this.repeatMode === 'one') {
            this.audio.currentTime = 0;
            this.play();
        } else if (this.repeatMode === 'all' || this.currentSongIndex < this.currentPlaylist.length - 1) {
            this.nextSong();
        } else {
            this.pause();
        }
    }

    handleAudioError(error) {
        console.error('Audio error:', error);
        this.showNotification('Error loading audio file');
        this.nextSong();
    }

    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        document.documentElement.setAttribute('data-theme', newTheme);

        const icon = this.themeToggle.querySelector('i');
        icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';

        this.saveUserPreferences();
        this.showNotification(`${newTheme.charAt(0).toUpperCase() + newTheme.slice(1)} theme enabled`);
    }

    togglePlaylist() {
        this.playlistSection.classList.toggle('active');
        const isActive = this.playlistSection.classList.contains('active');

        const icon = this.playlistToggle.querySelector('i');
        icon.className = isActive ? 'fas fa-times' : 'fas fa-list';
    }

    switchTab(tabName) {
        this.currentTab = tabName;
        this.currentPlaylist = this.allSongs.filter(song => song.category === tabName);
        this.currentSongIndex = 0;

        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.toggle('active', content.id === `${tabName}Tab`);
        });

        this.updatePlaylistUI();
        this.loadCurrentSong();
    }

    selectSong(index) {
        this.currentSongIndex = index;
        this.loadCurrentSong();
        this.play();
    }

    updatePlaylistUI() {
        document.querySelectorAll('.song-item').forEach((item, index) => {
            item.classList.toggle('active', index === this.currentSongIndex);
        });
    }

    handleKeyboard(event) {
        switch(event.code) {
            case 'Space':
                event.preventDefault();
                this.togglePlay();
                break;
            case 'ArrowLeft':
                this.previousSong();
                break;
            case 'ArrowRight':
                this.nextSong();
                break;
            case 'ArrowUp':
                event.preventDefault();
                this.setVolume(Math.min(100, this.audio.volume * 100 + 10));
                break;
            case 'ArrowDown':
                event.preventDefault();
                this.setVolume(Math.max(0, this.audio.volume * 100 - 10));
                break;
        }
    }

    startVisualizer() {
        const bars = document.querySelectorAll('.visualizer-bar');
        this.visualizerInterval = setInterval(() => {
            bars.forEach(bar => {
                const height = Math.random() * 100;
                bar.style.height = `${height}%`;
            });
        }, 100);
    }

    stopVisualizer() {
        if (this.visualizerInterval) {
            clearInterval(this.visualizerInterval);
            const bars = document.querySelectorAll('.visualizer-bar');
            bars.forEach(bar => {
                bar.style.height = '20%';
            });
        }
    }

    showNotification(message) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: var(--shadow-medium);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    saveUserPreferences() {
        const preferences = {
            volume: this.audio.volume,
            theme: document.documentElement.getAttribute('data-theme'),
            isShuffled: this.isShuffled,
            repeatMode: this.repeatMode
        };
        localStorage.setItem('pakistaniMusicPlayerPrefs', JSON.stringify(preferences));
    }

    loadUserPreferences() {
        const saved = localStorage.getItem('pakistaniMusicPlayerPrefs');
        if (saved) {
            const preferences = JSON.parse(saved);

            // Set volume
            this.setVolume(preferences.volume * 100);

            // Set theme
            if (preferences.theme) {
                document.documentElement.setAttribute('data-theme', preferences.theme);
                const icon = this.themeToggle.querySelector('i');
                icon.className = preferences.theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }

            // Set shuffle
            this.isShuffled = preferences.isShuffled || false;
            this.shuffleBtn.classList.toggle('active', this.isShuffled);

            // Set repeat mode
            this.repeatMode = preferences.repeatMode || 'none';
            this.repeatBtn.classList.toggle('active', this.repeatMode !== 'none');
        }

        // Load first song
        if (this.currentPlaylist.length > 0) {
            this.loadCurrentSong();
        }
    }
}

// Initialize the music player when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new PakistaniMusicPlayer();
});

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
